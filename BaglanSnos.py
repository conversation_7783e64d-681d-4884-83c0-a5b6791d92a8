# Baglan Snos - Türkmen dilinde ý<PERSON>lan
# Ýasaýjy: wndkx söýgüsi bilen
import aiohttp
import pystyle
import asyncio
import fake_useragent
import requests
import random
import sys
import os

# Tötänleýin telefon belgisi döretmek üçin
def tötänleýin_telefon():
    ýurt_kodlary = ["+993", "+7", "+374", "+375", "+380", "+994", "+995", "+1"]
    return random.choice(ýurt_kodlary) + ''.join(random.choices('0123456789', k=10))

# Tötänleýin email döretmek üçin  
def tötänleýin_email():
    üpjün_edijiler = ["@gmail.com", '@hotmail.com', '@icloud.com', "@outlook.com", '@mail.ru', '@yahoo.com']
    return ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789._', k=random.randint(5,20))) + random.choice(üpjün_edijiler)

# POST habaryny ibermek üçin
async def post_habar_iber(url, maglumatlar):
    ua = fake_useragent.UserAgent()
    ulanyjy_agenti = ua.random
    async with aiohttp.ClientSession(headers={"User-Agent": ulanyjy_agenti}) as sessiýa:
        jogap = await sessiýa.post(url, data=maglumatlar)
        if jogap.status == 200:
            pystyle.Write.Print('✅ Şikaýat iberildi!\n', pystyle.Colors.cyan, interval=0.0001)
        else:
            pystyle.Write.Print("❌ Şikaýat iberip bolmady\n", pystyle.Colors.red, interval=0.0001)

# Şikaýatlary ibermek üçin esasy funksiýa
async def iber(tekst):
    mukdar = int(pystyle.Write.Input("\n🔢 Şikaýatlaryň sany: ", pystyle.Colors.cyan, interval=0.0001))
    for _ in range(1, mukdar+1):
        email = tötänleýin_email()
        telefon = tötänleýin_telefon()
        await post_habar_iber('https://telegram.org/support', {
            "message": tekst,
            "email": email,
            "phone": telefon
        })

# Esasy menýu görkezmek üçin
def menýu():
    pystyle.System.Clear()
    # Suratyňyzdaky gök-ýaşyl reňkleri ulanyp
    pystyle.Write.Print("""
    ╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
    ║                                                                                                              ║
    ║    ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗    ███████╗███╗   ██╗ ██████╗ ███████╗              ║
    ║    ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║    ██╔════╝████╗  ██║██╔═══██╗██╔════╝              ║
    ║    ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║    ███████╗██╔██╗ ██║██║   ██║███████╗              ║
    ║    ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║    ╚════██║██║╚██╗██║██║   ██║╚════██║              ║
    ║    ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║    ███████║██║ ╚████║╚██████╔╝███████║              ║
    ║    ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝    ╚══════╝╚═╝  ╚═══╝ ╚═════╝ ╚══════╝              ║
    ║                                                                                                              ║
    ║                                    🇹🇲 Türkmen Wersiýasy 🇹🇲                                                ║
    ║                              Ýasaýjy: Benjamin Franklin | Goldaw Start: @onion                                   ║
    ║                                                                                                              ║
    ║══════════════════════════════════════════════════════════════════════════════════════════════════════════════║
    ║                                                                                                              ║
    ║    [1] 👤 Hasaby baglamak              [4] 💬 Topar baglamak                                                ║
    ║    [2] 📺 Kanal baglamak               [5] ✍️  Öz tekstiňiz                                                 ║
    ║    [3] 🤖 Bot baglamak                 [6] 🚪 Çykmak                                                        ║
    ║                                                                                                              ║
    ╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
""", pystyle.Colors.cyan, interval=0.0001)
    
if __name__ == '__main__':
    while True:
        try:
            menýu()
            esasy_saýlaw = int(pystyle.Write.Input("🎯 Opsiýany saýlaň: ", pystyle.Colors.cyan, interval=0.0001))
            if esasy_saýlaw == 1:
                pystyle.Write.Print("📋 Aşakdaky maglumatlary giriziň:\n", pystyle.Colors.cyan, interval=0.0001)
                ulanyjy_ady = pystyle.Write.Input("👤 Ulanyjy ady: ", pystyle.Colors.cyan, interval=0.0001)
                ulanyjy_id = pystyle.Write.Input("🆔 Ulanyjy ID: ", pystyle.Colors.cyan, interval=0.0001)
                çat_baglanyşygy = pystyle.Write.Input("💬 Çat baglanyşygy: ", pystyle.Colors.cyan, interval=0.0001)
                bozgunçylyk_baglanyşygy = pystyle.Write.Input("⚠️  Bozgunçylyk baglanyşygy: ", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("🎯 Indi şu opsiýalaryň birini saýlaň:\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("1. 📧 Spam\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("2. 🔍 Şahsy maglumatlary ýaýratmak\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("3. 😈 Trolling\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("4. 🔐 Sessiýalary ýapmak\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("5. 💎 Premium bilen\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("6. 📱 Wirtual nomer bilen\n", pystyle.Colors.cyan, interval=0.0001)
                opsiýa = int(pystyle.Write.Input("➤ ", pystyle.Colors.cyan, interval=0.0001))
                if opsiýa >= 1 and opsiýa <= 3:
                    şikaýat_tekstleri = {
                            1: f"Hormatly goldaw gullugy! Siziň platformaňyzda köpçülikleýin spam ibermek bilen meşgullanýan ulanyjy bilen duşuşdym. Onuň ulanyjy ady - {ulanyjy_ady}, ID - {ulanyjy_id}, çat baglanyşygy - {çat_baglanyşygy}, bozgunçylyk mysaly - {bozgunçylyk_baglanyşygy}. Bu meselä seredip, degişli çäreleri görmegi haýyş edýärin.",
                            2: f"Hormatly goldaw gullugy! Platformaňyzda eýeleriniň razylygy bolmazdan şahsy maglumatlary ýaýradýan ulanyjy tapdym. Ulanyjy ady - {ulanyjy_ady}, ID - {ulanyjy_id}, çat baglanyşygy - {çat_baglanyşygy}, bozgunçylyk mysaly - {bozgunçylyk_baglanyşygy}. Bu hadysa çuňňur seredip, hasaby petiklemek ýaly çäreleri görmegi haýyş edýärin.",
                            3: f"Salam, hormatly Telegram goldaw gullugy! Platformaňyzda edepsiz sözleri ulanýan we çatlarda spam ibermek bilen meşgullanýan ulanyjy gördüm. Ulanyjy ady - {ulanyjy_ady}, ID - {ulanyjy_id}, çat baglanyşygy - {çat_baglanyşygy}, bozgunçylyk mysaly - {bozgunçylyk_baglanyşygy}. Bu ýagdaýa jogap berip, hasaby petiklemek ýaly çäreleri görmegi umyt edýärin."
                    }
                    asyncio.run(iber(şikaýat_tekstleri[opsiýa]))
                elif opsiýa == 4:
                    pystyle.Write.Print("📋 Aşakdaky maglumatlary giriziň:\n", pystyle.Colors.cyan, interval=0.0001)
                    ulanyjy_ady = pystyle.Write.Input("👤 Ulanyjy ady: ", pystyle.Colors.cyan, interval=0.0001)
                    ulanyjy_id = pystyle.Write.Input("🆔 Ulanyjy ID: ", pystyle.Colors.cyan, interval=0.0001)
                    asyncio.run(iber(f"Hormatly goldaw gullugy! Kömek soraýaryn. Düýn tötänleýin phishing baglanyşygyna basyp, hasabymyň giriş mümkinçiligini ýitirdim. Meniň ulanyjy adym - {ulanyjy_ady}, ID - {ulanyjy_id}. Bu hasaby aýyrmagyňyzy ýa-da ähli sessiýalary täzeden düzmegi haýyş edýärin. Çalt garaşylmagyňyz üçin öňünden sag boluň."))
                elif opsiýa == 5 or opsiýa == 6:
                    pystyle.Write.Print("📋 Aşakdaky maglumatlary giriziň:\n", pystyle.Colors.cyan, interval=0.0001)
                    ulanyjy_ady = pystyle.Write.Input("👤 Ulanyjy ady: ", pystyle.Colors.cyan, interval=0.0001)
                    ulanyjy_id = pystyle.Write.Input("🆔 Ulanyjy ID: ", pystyle.Colors.cyan, interval=0.0001)
                    şikaýat_tekstleri = {
                        5: f"Salam, Telegram goldaw gullugy! {ulanyjy_ady} ({ulanyjy_id}) hasapli ulanyjynyň ýörite saýtdan satyn alnan wirtual nomer ulanýandygyny habar bermek isleýärin. Meniň pikirimiçe, bu nomer oňa hiç hili degişli däl. Bu ýagdaýa seredip, çözgüt tapmagyňyzy haýyş edýärin!",
                        6: f"Hormatly Telegram goldaw gullugy! {ulanyjy_ady} ({ulanyjy_id}) hasapli ulanyjynyň spam habarlary ibermek we Telegram çäklerini aýlanmak üçin premium hasap satyn alandygyny bildim. Bu maglumaty barlamagyňyzy we degişli çäreleri görmegi haýyş edýärin."
                    }
                    asyncio.run(iber(şikaýat_tekstleri[opsiýa]))
            if esasy_saýlaw == 2:
                pystyle.Write.Print("📺 Kanal baglamak sebäpleri:\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("1. 🔍 Şahsy maglumatlar\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("2. 🐾 Haýwanlara zyýan bermek\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("3. 🚫 Çagalar üçin zyýanly mazmun\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("4. 💰 Baha kanallary\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("5. 💊 Neşe serişdeleri\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("6. 🔞 18+ (şok mazmun, pornografiýa we ş.m.)\n", pystyle.Colors.cyan, interval=0.0001)
                kanal_saýlawy = int(pystyle.Write.Input("🎯 Saýlaň: ", pystyle.Colors.cyan, interval=0.0001))

                if kanal_saýlawy in [1, 2, 3, 4, 5, 6]:
                    kanal_baglanyşygy = pystyle.Write.Input("📺 Kanal baglanyşygy: ", pystyle.Colors.cyan, interval=0.0001)
                    bozgunçylyk_baglanyşygy = pystyle.Write.Input("⚠️  Bozgunçylyk baglanyşygy (kanalda): ", pystyle.Colors.cyan, interval=0.0001)
                    pystyle.Write.Print("⏳ Garaşyň...", pystyle.Colors.cyan, interval=0.0001)
                    kanal_şikaýat_tekstleri = {
                        1: f"Hormatly Telegram goldaw gullugy! Platformaňyzda bigünä adamlaryň şahsy maglumatlaryny ýaýradýan kanal tapdym. Kanal baglanyşygy - {kanal_baglanyşygy}, bozgunçylyk mysallary - {bozgunçylyk_baglanyşygy}. Bu kanaly derrew petiklemegi haýyş edýärin.",
                        2: f"Salam, hormatly Telegram goldaw gullugy! Gynansagam, platformaňyzda haýwanlara rehimsizlik bilen garaýan mazmun ýaýradýan kanal tapdym. Kanal baglanyşygy - {kanal_baglanyşygy}, tassyklaýjy materiallar - {bozgunçylyk_baglanyşygy}. Bu kanaly gyssagly petiklemegi umyt edýärin.",
                        3: f"Hormatly Telegram goldaw gullugy! Platformaňyzda kämillik ýaşyna ýetmedik çagalar gatnaşan pornografik mazmun ýaýradýan kanal tapdym. Kanal baglanyşygy - {kanal_baglanyşygy}, bozgunçylyk mysallary - {bozgunçylyk_baglanyşygy}. Bu kanaly mümkin boldugyça çalt petiklemegi haýyş edýärin.",
                        4: f"Salam, hormatly Telegram moderatory! Messenjerňizde doksing we swatting hyzmatlary berýän kanal hakda şikaýat etmek isleýärin. Kanal baglanyşygy - {kanal_baglanyşygy}, tassyklaýjy materiallar - {bozgunçylyk_baglanyşygy}. Bu kanaly derrew petiklemegi haýyş edýärin.",
                        5: f"Hormatly goldaw gullugy! Telegram messenjerňizde bikanun neşe serişdelerini satýan kanal tapdym. Kanal ID - {kanal_baglanyşygy}, bozgunçylyk baglanyşygy - {bozgunçylyk_baglanyşygy}. Bu meselä seredip, kanaly petiklemek üçin degişli çäreleri görmegi haýyş edýärin.",
                        6: f"Salam, hormatly Telegram moderatory! Messenjerňizde 18+ we şok materiallary köpçülige ýaýradýan kanal hakda şikaýat etmek isleýärin. Mümkin boldugyça çalt çäre görmegi haýyş edýärin. Kanal baglanyşygy: {kanal_baglanyşygy}. Bozgunçylyk baglanyşygy: {bozgunçylyk_baglanyşygy}"
                    }
                    asyncio.run(iber(kanal_şikaýat_tekstleri[kanal_saýlawy]))
            if esasy_saýlaw == 3:
                pystyle.Write.Print("🤖 Bot baglamak sebäpleri:\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("1. 🔍 OSINT (şahsy maglumat gözlegi)\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("2. 💊 Neşe dükany\n", pystyle.Colors.cyan, interval=0.0001)
                bot_saýlawy = int(pystyle.Write.Input("🎯 Saýlaň: ", pystyle.Colors.cyan, interval=0.0001))
                if bot_saýlawy in [1,2]:
                    bot_ulanyjysy = pystyle.Write.Input("🤖 Bot ulanyjy ady: ", pystyle.Colors.cyan, interval=0.0001)
                    pystyle.Write.Print("⏳ Garaşyň...", pystyle.Colors.cyan, interval=0.0001)
                    bot_şikaýat_tekstleri = {
                        1: f"Salam, hormatly Telegram goldaw gullugy! Platformaňyzda ulanyjylaryňyzyň şahsy maglumatlaryny gözleýän bot tapdym. Bot baglanyşygy - {bot_ulanyjysy}. Bu boty barlaň we petikläň.",
                        2: f"Salam! Messenjerňizde bikanun neşe söwdasy edýän bot bilen duşuşdym. Şikaýatyma jogap berip, bu boty petiklemek üçin çäre görmegi haýyş edýärin."
                    }
                    asyncio.run(iber(bot_şikaýat_tekstleri[bot_saýlawy]))
            if esasy_saýlaw == 4:
                pystyle.Write.Print("💬 Topar baglamak sebäpleri:\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("1. 📧 Spam\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("2. 🖼️  Awatar ýa-da at üçin\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("3. ⚔️  Zorluk propagandasy we ş.m.\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("4. 📈 Ýalňyş ýazylma\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("5. 😡 Kemsitmek\n", pystyle.Colors.cyan, interval=0.0001)
                topar_saýlawy = int(pystyle.Write.Input('🎯 Saýlaň: ', pystyle.Colors.cyan, interval=0.0001))
                if topar_saýlawy in [1,2,3,4]:
                    ulanyjy_çaty = pystyle.Write.Input('💬 Çat baglanyşygy: ', pystyle.Colors.cyan)
                    çat_id = pystyle.Write.Input('🆔 Çat ID: ', pystyle.Colors.cyan)
                    topar_şikaýat_tekstleri = {1: f"Salam, hormatly Telegram goldaw gullugy! Platformaňyzda spam habarlary iberýän topar tapdym. Topar baglanyşygy - {ulanyjy_çaty}, Topar ID - {çat_id}. Bu topara çäre görüp, mümkin boldugyça çalt petikläň.",
                        2: f"Salam, hormatly Telegram goldaw gullugy! Platformaňyzda jedelli awatar we at goýan topar tapdym. Topar baglanyşygy - {ulanyjy_çaty}, Topar ID - {çat_id}. Bu topara çäre görüp, mümkin boldugyça çalt petikläň.",
                        3: f"Salam, hormatly Telegram goldaw gullugy! Platformaňyzda zorluk we beýleki rehimsizlikleri wagyz edýän topar tapdym. Topar baglanyşygy - {ulanyjy_çaty}, Topar ID - {çat_id}. Bu topara çäre görüp, mümkin boldugyça çalt petikläň.",
                        4: f"Salam, hormatly Telegram goldaw gullugy! Platformaňyzda ýazylýanlary ýalňyş artdyrýan topar tapdym. Topar baglanyşygy - {ulanyjy_çaty}, Topar ID - {çat_id}. Bu topara çäre görüp, mümkin boldugyça çalt petikläň."
                    }
                    asyncio.run(iber(topar_şikaýat_tekstleri[topar_saýlawy]))
                if topar_saýlawy in [5]:
                    ulanyjy_çaty = pystyle.Write.Input('💬 Çat baglanyşygy: ', pystyle.Colors.cyan)
                    çat_id = pystyle.Write.Input('🆔 Çat ID: ', pystyle.Colors.cyan)
                    baglanyşyk = pystyle.Write.Input('⚠️  Bozgunçylyk baglanyşygy: ', pystyle.Colors.cyan)
                    asyncio.run(iber(f"Salam, hormatly Telegram goldaw gullugy! Adamlary kemsidýän we olara garşy edepsiz sözleri ulanýan topar tapdym. Topar baglanyşygy - {ulanyjy_çaty}, Topar ID - {çat_id}, Bozgunçylyk baglanyşygy - {baglanyşyk}. Bu topara çäre görüp, mümkin boldugyça çalt petikläň."))
            if esasy_saýlaw == 5:
                giriş = pystyle.Write.Input('✍️  Öz tekstiňizi giriziň: ', pystyle.Colors.cyan, interval=0.0001)
                asyncio.run(iber(giriş))
            if esasy_saýlaw == 6:
                pystyle.Write.Print("🚪 Hoş gal! Programma ýapylýar...", pystyle.Colors.cyan, interval=0.0001)
                sys.exit()
        except KeyboardInterrupt:
            pystyle.Write.Print("🛑 Programma ulanyjy tarapyndan ýapyldy!", pystyle.Colors.red, interval=0.0001)
            sys.exit()
        except ValueError:
            pystyle.Write.Print("❌ Nädogry giriş! Diňe san giriziň.", pystyle.Colors.red, interval=0.0001)
            input("⏎ Dowam etmek üçin Enter basyň...")
        except Exception as e:
            pystyle.Write.Print(f"❌ Ýalňyşlyk ýüze çykdy! {str(e)}\n", pystyle.Colors.red, interval=0.0001)
            input("⏎ Dowam etmek üçin Enter basyň...")
