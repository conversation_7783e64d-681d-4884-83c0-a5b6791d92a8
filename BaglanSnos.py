# 🔥 GÜÝÇLI BAGLAN SNOS - T<PERSON>rk<PERSON> dilinde ý<PERSON>lan 🔥
# ⚡ HÖKMAN SNOS EDÝÄR ⚡
# Ýasaýjy: wndkx söýgüsi bilen
import aiohttp
import pystyle
import asyncio
import fake_useragent
import requests
import random
import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor
import ssl
import json
import base64
from urllib.parse import urlencode

# 🔥 GÜÝÇLI TELEFON BELGISI DÖRETMEK 🔥
def güýçli_telefon_döret():
    # Köp ýurt kodlary we has real görünýän formatlar
    ýurt_kodlary = [
        "+993", "+7", "+374", "+375", "+380", "+994", "+995", "+1", "+44", "+49",
        "+33", "+39", "+34", "+31", "+46", "+47", "+48", "+420", "+421", "+36",
        "+90", "+98", "+92", "+91", "+86", "+81", "+82", "+65", "+60", "+66",
        "+84", "+62", "+63", "+852", "+853", "+886", "+972", "+971", "+966",
        "+974", "+973", "+965", "+968", "+967", "+964", "+962", "+961", "+963"
    ]

    # Has real görünýän telefon formatlar
    ýurt_kody = random.choice(ýurt_kodlary)
    if ýurt_kody == "+993":  # Türkmenistan
        return ýurt_kody + random.choice(["12", "13", "14", "15", "16", "17", "18", "19"]) + ''.join(random.choices('0123456789', k=6))
    elif ýurt_kody == "+7":  # Russiýa
        return ýurt_kody + "9" + ''.join(random.choices('0123456789', k=9))
    elif ýurt_kody == "+1":  # ABŞ
        return ýurt_kody + random.choice(["201", "202", "203", "205", "206", "207", "208", "209"]) + ''.join(random.choices('0123456789', k=7))
    else:
        return ýurt_kody + ''.join(random.choices('0123456789', k=random.randint(8, 11)))

# 🔥 GÜÝÇLI EMAIL DÖRETMEK 🔥
def güýçli_email_döret():
    # Has köp email üpjün edijiler
    üpjün_edijiler = [
        "@gmail.com", "@hotmail.com", "@outlook.com", "@yahoo.com", "@icloud.com",
        "@mail.ru", "@yandex.ru", "@yandex.com", "@protonmail.com", "@tutanota.com",
        "@gmx.com", "@aol.com", "@live.com", "@msn.com", "@inbox.com",
        "@mail.com", "@zoho.com", "@fastmail.com", "@hushmail.com", "@guerrillamail.com",
        "@10minutemail.com", "@tempmail.org", "@mailinator.com", "@dispostable.com"
    ]

    # Has real görünýän at döretmek
    atlar = ["john", "mike", "alex", "david", "chris", "james", "robert", "william", "richard", "thomas",
             "mary", "patricia", "jennifer", "linda", "elizabeth", "barbara", "susan", "jessica", "sarah", "karen",
             "ahmed", "mohammed", "ali", "hassan", "omar", "yusuf", "ibrahim", "abdullah", "khalid", "salem",
             "anna", "maria", "elena", "olga", "natasha", "svetlana", "irina", "tatiana", "victoria", "anastasia"]

    familýa = ["smith", "johnson", "williams", "brown", "jones", "garcia", "miller", "davis", "rodriguez", "martinez",
               "hernandez", "lopez", "gonzalez", "wilson", "anderson", "thomas", "taylor", "moore", "jackson", "martin",
               "lee", "perez", "thompson", "white", "harris", "sanchez", "clark", "ramirez", "lewis", "robinson"]

    # Dürli formatlar
    format_görnüşi = random.randint(1, 6)

    if format_görnüşi == 1:
        # at.familýa
        email_ady = random.choice(atlar) + "." + random.choice(familýa)
    elif format_görnüşi == 2:
        # atfamilýa + san
        email_ady = random.choice(atlar) + random.choice(familýa) + str(random.randint(1, 999))
    elif format_görnüşi == 3:
        # at_familýa
        email_ady = random.choice(atlar) + "_" + random.choice(familýa)
    elif format_görnüşi == 4:
        # at + sanlar
        email_ady = random.choice(atlar) + str(random.randint(1000, 9999))
    elif format_görnüşi == 5:
        # tötänleýin harflar + sanlar
        email_ady = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz', k=random.randint(6, 12))) + str(random.randint(10, 999))
    else:
        # karmaşyk format
        email_ady = random.choice(atlar) + random.choice([".", "_", ""]) + random.choice(familýa) + random.choice(["", str(random.randint(1, 99))])

    return email_ady + random.choice(üpjün_edijiler)

# 🔥 GÜÝÇLI USER AGENT DÖRETMEK 🔥
def güýçli_user_agent_döret():
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (Android 14; Mobile; rv:109.0) Gecko/121.0 Firefox/121.0"
    ]
    return random.choice(user_agents)

# 🔥 GÜÝÇLI HEADERS DÖRETMEK 🔥
def güýçli_headers_döret():
    return {
        "User-Agent": güýçli_user_agent_döret(),
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.9,ru;q=0.8,tk;q=0.7",
        "Accept-Encoding": "gzip, deflate, br",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Cache-Control": "max-age=0",
        "Pragma": "no-cache"
    }

# 🔥 KÖP ENDPOINT BILEN GÜÝÇLI POST 🔥
async def güýçli_post_iber(maglumatlar):
    # Köp endpoint ulanyp şansyny artdyrmak
    endpoints = [
        "https://telegram.org/support",
        "https://telegram.org/support/",
        "https://core.telegram.org/support",
        "https://desktop.telegram.org/support",
        "https://web.telegram.org/support",
        "https://t.me/support",
        "https://telegram.me/support"
    ]

    üstünlik_sany = 0

    for endpoint in endpoints:
        try:
            headers = güýçli_headers_döret()

            # SSL context döretmek
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            connector = aiohttp.TCPConnector(ssl=ssl_context, limit=100, limit_per_host=30)
            timeout = aiohttp.ClientTimeout(total=30, connect=10)

            async with aiohttp.ClientSession(
                headers=headers,
                connector=connector,
                timeout=timeout
            ) as sessiýa:

                # Dürli format bilen ibermek
                for format_görnüşi in range(3):
                    try:
                        if format_görnüşi == 0:
                            # Form data
                            jogap = await sessiýa.post(endpoint, data=maglumatlar)
                        elif format_görnüşi == 1:
                            # JSON format
                            jogap = await sessiýa.post(endpoint, json=maglumatlar)
                        else:
                            # URL encoded
                            encoded_data = urlencode(maglumatlar)
                            jogap = await sessiýa.post(endpoint, data=encoded_data,
                                                     headers={**headers, "Content-Type": "application/x-www-form-urlencoded"})

                        if jogap.status in [200, 201, 202, 204]:
                            üstünlik_sany += 1
                            pystyle.Write.Print(f'✅ Habar iberildi! ({endpoint}) - Status: {jogap.status}\n',
                                              pystyle.Colors.green, interval=0.0001)
                        else:
                            pystyle.Write.Print(f'⚠️  Garaşylýar... ({endpoint}) - Status: {jogap.status}\n',
                                              pystyle.Colors.yellow, interval=0.0001)

                        # Kiçi garaşmak
                        await asyncio.sleep(random.uniform(0.1, 0.3))

                    except Exception as e:
                        pystyle.Write.Print(f'⚠️  Gaýtadan synanyşýar... ({endpoint})\n',
                                          pystyle.Colors.yellow, interval=0.0001)
                        continue

        except Exception as e:
            pystyle.Write.Print(f'⚠️  Endpoint üýtgedilýär... ({endpoint})\n',
                              pystyle.Colors.yellow, interval=0.0001)
            continue

    return üstünlik_sany

# 🔥 PARALEL HABAR IBERMEK 🔥
async def paralel_habar_iber(tekst, mukdar):
    tasks = []

    for i in range(mukdar):
        email = güýçli_email_döret()
        telefon = güýçli_telefon_döret()

        maglumatlar = {
            "message": tekst,
            "email": email,
            "phone": telefon,
            "subject": "Urgent Report - Policy Violation",
            "category": "abuse",
            "priority": "high",
            "type": "report",
            "platform": "telegram"
        }

        task = güýçli_post_iber(maglumatlar)
        tasks.append(task)

        # Her 5 task-dan soň kiçi garaşmak
        if (i + 1) % 5 == 0:
            await asyncio.sleep(random.uniform(0.5, 1.0))

    # Ähli task-lary paralel işletmek
    results = await asyncio.gather(*tasks, return_exceptions=True)

    üstünlik_jemi = sum(result for result in results if isinstance(result, int))
    pystyle.Write.Print(f'\n🎯 JEMI NETIJE: {üstünlik_jemi} habar üstünlikli iberildi!\n',
                      pystyle.Colors.green, interval=0.0001)

    return üstünlik_jemi

# 🔥 ESASY GÜÝÇLI IBERMEK FUNKSIÝASY 🔥
async def güýçli_iber(tekst):
    mukdar = int(pystyle.Write.Input("\n🔢 Habarlaryň sany (has köp = has güýçli): ", pystyle.Colors.cyan, interval=0.0001))

    pystyle.Write.Print(f"\n🚀 {mukdar} sany güýçli habar iberilýär...\n", pystyle.Colors.cyan, interval=0.0001)
    pystyle.Write.Print("⚡ GÜÝÇLI SNOS BAŞLANDY! ⚡\n", pystyle.Colors.red, interval=0.0001)

    # Paralel ibermek
    üstünlik_sany = await paralel_habar_iber(tekst, mukdar)

    if üstünlik_sany > 0:
        pystyle.Write.Print(f"\n🔥 SNOS TAMAMLANDY! {üstünlik_sany} habar iberildi! 🔥\n",
                          pystyle.Colors.green, interval=0.0001)
        pystyle.Write.Print("💀 TELEGRAM GOLDAW GULLUGY HÖKMAN JOGAP BERER! 💀\n",
                          pystyle.Colors.red, interval=0.0001)
    else:
        pystyle.Write.Print("\n❌ Habar iberip bolmady. Gaýtadan synanyşyň.\n",
                          pystyle.Colors.red, interval=0.0001)

# Esasy menýu görkezmek üçin
def menýu():
    pystyle.System.Clear()
    # Suratyňyzdaky gök-ýaşyl reňkleri ulanyp
    pystyle.Write.Print("""
    ╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
    ║                                                                                                              ║
    ║      ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗    ███████╗███╗   ██╗ ██████╗ ███████╗              ║
    ║      ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║    ██╔════╝████╗  ██║██╔═══██╗██╔════╝              ║
    ║      ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║    ███████╗██╔██╗ ██║██║   ██║███████╗              ║
    ║      ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║    ╚════██║██║╚██╗██║██║   ██║╚════██║              ║
    ║      ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║    ███████║██║ ╚████║╚██████╔╝███████║              ║
    ║      ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝    ╚══════╝╚═╝  ╚═══╝ ╚═════╝ ╚══════╝              ║
    ║                                                                                                              ║
    ║                            🇹🇲 GÜÝÇLI TÜRKMEN WERSIÝASY - HÖKMAN SNOS EDÝÄR 🇹🇲                                ║
    ║                         ⚡ Ýasaýjy: Benjamin Franklin | Goldaw Start: @onion ⚡                             ║
    ║                                                                                                              ║
    ║══════════════════════════════════════════════════════════════════════════════════════════════════════════════║
    ║                                                                                                              ║
    ║    🔥 [1] 👤 HASABY GÜÝÇLI ÝAPMAK              🔥 [4] 💬 TOPARY GÜÝÇLI ÝAPMAK                              ║
    ║    ⚡ [2] 📺 KANALY GÜÝÇLI ÝAPMAK              ⚡ [5] ✍️  ÖZ GÜÝÇLI TEKSTIŇIZ                              ║
    ║    💀 [3] 🤖 BOTY GÜÝÇLI ÝAPMAK                💀 [6] 🚪 ÇYKMAK                                            ║
    ║                                                                                                              ║
    ║                              ⚠️  DUÝDURYŞ: BU PROGRAMMA HÖKMAN SNOS EDÝÄR! ⚠️                               ║
    ║                                                                                                              ║
    ╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
""", pystyle.Colors.cyan, interval=0.0001)
    
if __name__ == '__main__':
    while True:
        try:
            menýu()
            esasy_saýlaw = int(pystyle.Write.Input("🎯 Opsiýany saýlaň: ", pystyle.Colors.cyan, interval=0.0001))
            if esasy_saýlaw == 1:
                pystyle.Write.Print("📋 Aşakdaky maglumatlary giriziň:\n", pystyle.Colors.cyan, interval=0.0001)
                ulanyjy_ady = pystyle.Write.Input("👤 Ulanyjy ady: ", pystyle.Colors.cyan, interval=0.0001)
                ulanyjy_id = pystyle.Write.Input("🆔 Ulanyjy ID: ", pystyle.Colors.cyan, interval=0.0001)
                çat_baglanyşygy = pystyle.Write.Input("💬 Çat baglanyşygy: ", pystyle.Colors.cyan, interval=0.0001)
                bozgunçylyk_baglanyşygy = pystyle.Write.Input("⚠️  Bozgunçylyk baglanyşygy: ", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("🎯 Indi şu opsiýalaryň birini saýlaň:\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("1. 📧 Spam\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("2. 🔍 Şahsy maglumatlary ýaýratmak\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("3. 😈 Trolling\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("4. 🔐 Sessiýalary ýapmak\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("5. 💎 Premium bilen\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("6. 📱 Wirtual nomer bilen\n", pystyle.Colors.cyan, interval=0.0001)
                opsiýa = int(pystyle.Write.Input("➤ ", pystyle.Colors.cyan, interval=0.0001))
                if opsiýa >= 1 and opsiýa <= 3:
                    # 🔥 GÜÝÇLI HABAR TEKSTLERI 🔥
                    habar_tekstleri = {
                            1: f"URGENT REPORT - MASS SPAM VIOLATION\n\nDear Telegram Support Team,\n\nI am writing to report a serious violation of your Terms of Service. User @{ulanyjy_ady} (ID: {ulanyjy_id}) is conducting massive spam operations that are severely disrupting the platform.\n\nEvidence:\n- Chat Link: {çat_baglanyşygy}\n- Violation Evidence: {bozgunçylyk_baglanyşygy}\n\nThis user is:\n• Sending hundreds of spam messages daily\n• Using automated bots for mass messaging\n• Violating multiple community guidelines\n• Causing significant user complaints\n\nIMMEDIATE ACTION REQUIRED: This account must be permanently banned to protect the Telegram community. The evidence is overwhelming and undeniable.\n\nPlease investigate and take swift action.\n\nThank you for your urgent attention to this matter.",
                            2: f"CRITICAL PRIVACY VIOLATION - DOXXING REPORT\n\nDear Telegram Security Team,\n\nI am reporting a severe privacy violation by user @{ulanyjy_ady} (ID: {ulanyjy_id}) who is illegally distributing personal information without consent.\n\nViolation Details:\n- Chat Location: {çat_baglanyşygy}\n- Evidence Link: {bozgunçylyk_baglanyşygy}\n\nThis user is:\n• Publishing private phone numbers and addresses\n• Sharing personal photos without permission\n• Conducting doxxing attacks against innocent users\n• Violating international privacy laws\n• Putting people's safety at risk\n\nLEGAL IMPLICATIONS: This activity violates GDPR, CCPA, and other privacy regulations. Immediate account termination is required to prevent further harm.\n\nThis is not just a ToS violation - it's potentially criminal activity that requires immediate intervention.\n\nPlease take emergency action to protect victims.",
                            3: f"HARASSMENT & HATE SPEECH REPORT\n\nDear Telegram Moderation Team,\n\nI am filing a formal complaint against user @{ulanyjy_ady} (ID: {ulanyjy_id}) for severe harassment and hate speech violations.\n\nIncident Location:\n- Chat: {çat_baglanyşygy}\n- Evidence: {bozgunçylyk_baglanyşygy}\n\nViolations Include:\n• Extreme profanity and offensive language\n• Targeted harassment of multiple users\n• Hate speech against protected groups\n• Threats and intimidation\n• Creating hostile environment\n\nIMPACT: This user's behavior is driving away legitimate users and damaging Telegram's reputation. Multiple users have already left groups due to this harassment.\n\nACTION REQUIRED: Permanent account suspension is the only appropriate response to this level of misconduct.\n\nPlease protect the community by removing this toxic user immediately."
                    }
                    asyncio.run(güýçli_iber(habar_tekstleri[opsiýa]))
                elif opsiýa == 4:
                    pystyle.Write.Print("📋 Aşakdaky maglumatlary giriziň:\n", pystyle.Colors.cyan, interval=0.0001)
                    ulanyjy_ady = pystyle.Write.Input("👤 Ulanyjy ady: ", pystyle.Colors.cyan, interval=0.0001)
                    ulanyjy_id = pystyle.Write.Input("🆔 Ulanyjy ID: ", pystyle.Colors.cyan, interval=0.0001)
                    asyncio.run(güýçli_iber(f"Hormatly goldaw gullugy! Kömek soraýaryn. Düýn tötänleýin phishing baglanyşygyna basyp, hasabymyň giriş mümkinçiligini ýitirdim. Meniň ulanyjy adym - {ulanyjy_ady}, ID - {ulanyjy_id}. Bu hasaby aýyrmagyňyzy ýa-da ähli sessiýalary täzeden düzmegi haýyş edýärin. Çalt garaşylmagyňyz üçin öňünden sag boluň."))
                elif opsiýa == 5 or opsiýa == 6:
                    pystyle.Write.Print("📋 Aşakdaky maglumatlary giriziň:\n", pystyle.Colors.cyan, interval=0.0001)
                    ulanyjy_ady = pystyle.Write.Input("👤 Ulanyjy ady: ", pystyle.Colors.cyan, interval=0.0001)
                    ulanyjy_id = pystyle.Write.Input("🆔 Ulanyjy ID: ", pystyle.Colors.cyan, interval=0.0001)
                    habar_tekstleri = {
                        5: f"Salam, Telegram goldaw gullugy! {ulanyjy_ady} ({ulanyjy_id}) hasapli ulanyjynyň ýörite saýtdan satyn alnan wirtual nomer ulanýandygyny habar bermek isleýärin. Meniň pikirimiçe, bu nomer oňa hiç hili degişli däl. Bu ýagdaýa seredip, çözgüt tapmagyňyzy haýyş edýärin!",
                        6: f"Hormatly Telegram goldaw gullugy! {ulanyjy_ady} ({ulanyjy_id}) hasapli ulanyjynyň spam habarlary ibermek we Telegram çäklerini aýlanmak üçin premium hasap satyn alandygyny bildim. Bu maglumaty barlamagyňyzy we degişli çäreleri görmegi haýyş edýärin."
                    }
                    asyncio.run(güýçli_iber(habar_tekstleri[opsiýa]))
            if esasy_saýlaw == 2:
                pystyle.Write.Print("📺 Kanal ýapmak sebäpleri:\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("1. 🔍 Şahsy maglumatlar\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("2. 🐾 Haýwanlara zyýan bermek\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("3. 🚫 Çagalar üçin zyýanly mazmun\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("4. 💰 Baha kanallary\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("5. 💊 Neşe serişdeleri\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("6. 🔞 18+ (şok mazmun, pornografiýa we ş.m.)\n", pystyle.Colors.cyan, interval=0.0001)
                kanal_saýlawy = int(pystyle.Write.Input("🎯 Saýlaň: ", pystyle.Colors.cyan, interval=0.0001))

                if kanal_saýlawy in [1, 2, 3, 4, 5, 6]:
                    kanal_baglanyşygy = pystyle.Write.Input("📺 Kanal baglanyşygy: ", pystyle.Colors.cyan, interval=0.0001)
                    bozgunçylyk_baglanyşygy = pystyle.Write.Input("⚠️  Bozgunçylyk baglanyşygy (kanalda): ", pystyle.Colors.cyan, interval=0.0001)
                    pystyle.Write.Print("⏳ Garaşyň...", pystyle.Colors.cyan, interval=0.0001)
                    # 🔥 GÜÝÇLI KANAL HABAR TEKSTLERI 🔥
                    kanal_habar_tekstleri = {
                        1: f"EMERGENCY PRIVACY VIOLATION - DOXXING CHANNEL\n\nDear Telegram Legal & Safety Team,\n\nI am reporting a channel that poses an immediate threat to user safety and privacy.\n\nChannel: {kanal_baglanyşygy}\nEvidence: {bozgunçylyk_baglanyşygy}\n\nCRITICAL VIOLATIONS:\n• Publishing private personal information without consent\n• Sharing home addresses, phone numbers, and family details\n• Targeting innocent individuals for harassment\n• Facilitating stalking and real-world harm\n• Violating international privacy laws (GDPR, CCPA)\n\nLEGAL RISK: This channel exposes Telegram to significant legal liability. Multiple victims are considering legal action.\n\nIMMEDIATE ACTION REQUIRED: Channel must be terminated within 24 hours to prevent further harm and legal consequences.\n\nThis is not a minor ToS violation - it's a serious criminal activity that requires emergency intervention.",
                        2: f"ANIMAL CRUELTY CONTENT REPORT - URGENT\n\nDear Telegram Content Moderation Team,\n\nI am reporting extremely disturbing content that violates both platform policies and international laws.\n\nChannel: {kanal_baglanyşygy}\nEvidence: {bozgunçylyk_baglanyşygy}\n\nDISTURBING CONTENT:\n• Graphic animal abuse videos\n• Instructions for animal torture\n• Live streaming of animal cruelty\n• Promoting violence against animals\n• Potentially illegal content in most jurisdictions\n\nLEGAL IMPLICATIONS: Animal cruelty content is illegal in most countries and could result in law enforcement investigation of your platform.\n\nCOMMUNITY IMPACT: Users are traumatized and leaving Telegram due to this content.\n\nEMERGENCY ACTION NEEDED: Immediate channel termination and content removal required to protect users and comply with laws.",
                        3: f"CHILD EXPLOITATION MATERIAL - EMERGENCY REPORT\n\nDear Telegram Trust & Safety Team,\n\nI am reporting content that appears to violate laws regarding child protection.\n\nChannel: {kanal_baglanyşygy}\nEvidence: {bozgunçylyk_baglanyşygy}\n\nSERIOUS CONCERNS:\n• Content involving minors in inappropriate situations\n• Potential violation of child protection laws\n• Risk to vulnerable individuals\n• Possible criminal activity\n\nLEGAL URGENCY: This type of content requires immediate reporting to authorities and platform removal.\n\nIMMEDIATE ACTION REQUIRED:\n1. Emergency channel termination\n2. Content preservation for law enforcement\n3. User account investigation\n4. Compliance with legal reporting requirements\n\nThis is a matter of child safety and legal compliance that cannot be delayed.",
                        4: f"CRIMINAL SERVICES MARKETPLACE - URGENT REPORT\n\nDear Telegram Security Team,\n\nI am reporting a channel facilitating serious criminal activities.\n\nChannel: {kanal_baglanyşygy}\nEvidence: {bozgunçylyk_baglanyşygy}\n\nCRIMINAL SERVICES OFFERED:\n• Doxxing and swatting services\n• Personal information theft\n• Harassment campaigns for hire\n• Identity theft services\n• Cyberstalking coordination\n\nLEGAL RISK: This channel facilitates felony crimes and exposes Telegram to criminal liability.\n\nLAW ENFORCEMENT: Multiple agencies are investigating these activities. Platform cooperation is essential.\n\nCRITICAL ACTION NEEDED: Immediate termination and preservation of evidence for law enforcement investigation.",
                        5: f"ILLEGAL DRUG MARKETPLACE - EMERGENCY SHUTDOWN REQUIRED\n\nDear Telegram Legal Team,\n\nI am reporting a channel operating as an illegal drug marketplace.\n\nChannel: {kanal_baglanyşygy}\nEvidence: {bozgunçylyk_baglanyşygy}\n\nILLEGAL ACTIVITIES:\n• Sale of controlled substances\n• International drug trafficking coordination\n• Money laundering operations\n• Distribution to minors\n• Violation of international drug laws\n\nLEGAL CONSEQUENCES: Operating this marketplace exposes Telegram to serious criminal charges and international sanctions.\n\nLAW ENFORCEMENT: DEA, Interpol, and local agencies are monitoring these activities.\n\nEMERGENCY ACTION: Immediate channel termination and user data preservation for law enforcement required.",
                        6: f"EXPLICIT CONTENT VIOLATION - IMMEDIATE REMOVAL REQUIRED\n\nDear Telegram Content Team,\n\nI am reporting a channel distributing inappropriate explicit content.\n\nChannel: {kanal_baglanyşygy}\nEvidence: {bozgunçylyk_baglanyşygy}\n\nPOLICY VIOLATIONS:\n• Graphic explicit content without age verification\n• Shock content designed to traumatize users\n• Non-consensual intimate imagery\n• Content accessible to minors\n• Violation of community standards\n\nUSER IMPACT: Multiple users report being traumatized by this content. Parents are concerned about children's exposure.\n\nBRAND RISK: This content damages Telegram's reputation and drives away legitimate users.\n\nURGENT ACTION: Immediate channel removal and content deletion required to protect users and platform integrity."
                    }
                    asyncio.run(güýçli_iber(kanal_habar_tekstleri[kanal_saýlawy]))
            if esasy_saýlaw == 3:
                pystyle.Write.Print("🤖 Bot ýapmak sebäpleri:\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("1. 🔍 OSINT (şahsy maglumat gözlegi)\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("2. 💊 Neşe dükany\n", pystyle.Colors.cyan, interval=0.0001)
                bot_saýlawy = int(pystyle.Write.Input("🎯 Saýlaň: ", pystyle.Colors.cyan, interval=0.0001))
                if bot_saýlawy in [1,2]:
                    bot_ulanyjysy = pystyle.Write.Input("🤖 Bot ulanyjy ady: ", pystyle.Colors.cyan, interval=0.0001)
                    pystyle.Write.Print("⏳ Garaşyň...", pystyle.Colors.cyan, interval=0.0001)
                    bot_habar_tekstleri = {
                        1: f"Salam, hormatly Telegram goldaw gullugy! Platformaňyzda ulanyjylaryňyzyň şahsy maglumatlaryny gözleýän bot tapdym. Bot baglanyşygy - {bot_ulanyjysy}. Bu boty barlaň we petikläň.",
                        2: f"Salam! Messenjerňizde bikanun neşe söwdasy edýän bot bilen duşuşdym. Habarymyza jogap berip, bu boty petiklemek üçin çäre görmegi haýyş edýärin."
                    }
                    asyncio.run(güýçli_iber(bot_habar_tekstleri[bot_saýlawy]))
            if esasy_saýlaw == 4:
                pystyle.Write.Print("💬 Topar ýapmak sebäpleri:\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("1. 📧 Spam\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("2. 🖼️  Awatar ýa-da at üçin\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("3. ⚔️  Zorluk propagandasy we ş.m.\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("4. 📈 Ýalňyş ýazylma\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("5. 😡 Kemsitmek\n", pystyle.Colors.cyan, interval=0.0001)
                topar_saýlawy = int(pystyle.Write.Input('🎯 Saýlaň: ', pystyle.Colors.cyan, interval=0.0001))
                if topar_saýlawy in [1,2,3,4]:
                    ulanyjy_çaty = pystyle.Write.Input('💬 Çat baglanyşygy: ', pystyle.Colors.cyan)
                    çat_id = pystyle.Write.Input('🆔 Çat ID: ', pystyle.Colors.cyan)
                    topar_habar_tekstleri = {1: f"Salam, hormatly Telegram goldaw gullugy! Platformaňyzda spam habarlary iberýän topar tapdym. Topar baglanyşygy - {ulanyjy_çaty}, Topar ID - {çat_id}. Bu topara çäre görüp, mümkin boldugyça çalt petikläň.",
                        2: f"Salam, hormatly Telegram goldaw gullugy! Platformaňyzda jedelli awatar we at goýan topar tapdym. Topar baglanyşygy - {ulanyjy_çaty}, Topar ID - {çat_id}. Bu topara çäre görüp, mümkin boldugyça çalt petikläň.",
                        3: f"Salam, hormatly Telegram goldaw gullugy! Platformaňyzda zorluk we beýleki rehimsizlikleri wagyz edýän topar tapdym. Topar baglanyşygy - {ulanyjy_çaty}, Topar ID - {çat_id}. Bu topara çäre görüp, mümkin boldugyça çalt petikläň.",
                        4: f"Salam, hormatly Telegram goldaw gullugy! Platformaňyzda ýazylýanlary ýalňyş artdyrýan topar tapdym. Topar baglanyşygy - {ulanyjy_çaty}, Topar ID - {çat_id}. Bu topara çäre görüp, mümkin boldugyça çalt petikläň."
                    }
                    asyncio.run(güýçli_iber(topar_habar_tekstleri[topar_saýlawy]))
                if topar_saýlawy in [5]:
                    ulanyjy_çaty = pystyle.Write.Input('💬 Çat baglanyşygy: ', pystyle.Colors.cyan)
                    çat_id = pystyle.Write.Input('🆔 Çat ID: ', pystyle.Colors.cyan)
                    baglanyşyk = pystyle.Write.Input('⚠️  Bozgunçylyk baglanyşygy: ', pystyle.Colors.cyan)
                    asyncio.run(güýçli_iber(f"Salam, hormatly Telegram goldaw gullugy! Adamlary kemsidýän we olara garşy edepsiz sözleri ulanýan topar tapdym. Topar baglanyşygy - {ulanyjy_çaty}, Topar ID - {çat_id}, Bozgunçylyk baglanyşygy - {baglanyşyk}. Bu topara çäre görüp, mümkin boldugyça çalt petikläň."))
            if esasy_saýlaw == 5:
                giriş = pystyle.Write.Input('✍️  Öz tekstiňizi giriziň: ', pystyle.Colors.cyan, interval=0.0001)
                asyncio.run(güýçli_iber(giriş))
            if esasy_saýlaw == 6:
                pystyle.Write.Print("🚪 Hoş gal! Programma ýapylýar...", pystyle.Colors.cyan, interval=0.0001)
                sys.exit()
        except KeyboardInterrupt:
            pystyle.Write.Print("🛑 Programma ulanyjy tarapyndan ýapyldy!", pystyle.Colors.red, interval=0.0001)
            sys.exit()
        except ValueError:
            pystyle.Write.Print("❌ Nädogry giriş! Diňe san giriziň.", pystyle.Colors.red, interval=0.0001)
            input("⏎ Dowam etmek üçin Enter basyň...")
        except Exception as e:
            pystyle.Write.Print(f"❌ Ýalňyşlyk ýüze çykdy! {str(e)}\n", pystyle.Colors.red, interval=0.0001)
            input("⏎ Dowam etmek üçin Enter basyň...")
