# 🔥 START SNOS - Türkmenistanyň Start Messenger üçin 🔥
# ⚡ HÖKMAN SNOS EDÝÄR ⚡
# Ýasaýjy: <PERSON> s<PERSON>üsi bilen
import aiohttp
import pystyle
import asyncio
import fake_useragent
import requests
import random
import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor
import ssl
import json
import base64
from urllib.parse import urlencode

# 🇹🇲 TÜRKMEN ATLARY WE FAMILÝALARY 🇹🇲
türkmen_erkek_atlary = [
    "Ahmet", "<PERSON>hammet", "Orazmuhammet", "Gurbanguly", "Serdar", "Döwlet", "Gurban", 
    "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Batyr", "<PERSON>rogly", "<PERSON><PERSON>", "<PERSON><PERSON>", "O<PERSON><PERSON><PERSON>",
    "<PERSON>jep", "Saparmyrat", "Täçmyrat", "<PERSON><PERSON>", "Ýagmyr", "<PERSON>an", "<PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON>", "<PERSON><PERSON>z", "<PERSON><PERSON><PERSON>y<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"
]

türkmen_aýal_atlary = [
    "<PERSON>ýna", "Bibi", "Çinar", "Dünya", "Enesh", "<PERSON>ma", "Gülnar", "Haýat", "Jahan",
    "Kemine", "Leýla", "Maral", "Nazik", "<PERSON><PERSON>lgerek", "<PERSON><PERSON><PERSON>", "<PERSON>ozagül", "Şirin",
    "Tä<PERSON>", "<PERSON><PERSON>", "<PERSON>pagül", "Ýyldyz", "Zeýnep", "Aýgözel", "Bahar", "Çeper",
    "Dilber", "Ejegül", "Firuza", "Gözgül", "Hawa", "Jennet", "Kumri", "Lale"
]

türkmen_familýalary = [
    "Amanow", "Berdiýew", "Çaryýew", "Durdyýew", "Ezizoýew", "Gaýypow", "Hasanow",
    "Jahanow", "Kakajanow", "Lemaýew", "Meretow", "Nazyrýew", "Orazow", "Polatow",
    "Rustemow", "Şamyradow", "Täzeýew", "Urazow", "Weliýew", "Ýagmyrow", "Amanowa",
    "Berdiýewa", "Çaryýewa", "Durdyýewa", "Ezizoýewa", "Gaýypowa", "Hasanowa",
    "Jahanowa", "Kakajanowa", "Lemaýewa", "Meretowa", "Nazyrýewa", "Orazowa"
]

# 🔥 GÜÝÇLI TÜRKMEN TELEFON BELGISI DÖRETMEK 🔥
def türkmen_telefon_döret():
    # Türkmenistanyň telefon formatlar
    türkmen_operatorlar = [
        "+99361", "+99362", "+99363", "+99364", "+99365", "+99371"
    ]
    operator = random.choice(türkmen_operatorlar)
    nomer = ''.join(random.choices('0123456789', k=6))
    return operator + nomer

# 🔥 GÜÝÇLI TÜRKMEN EMAIL DÖRETMEK 🔥
def türkmen_email_döret():
    # Türkmen email üpjün edijiler
    türkmen_email_üpjünler = [
        "@mail.tm", "@tm.tm", "@turkmenistan.tm", "@sanly.tm", "@gmail.com", "@mail.ru", 
        "@yandex.ru", "@hotmail.com", "@outlook.com"
    ]
    
    # Türkmen at we familýa saýlamak
    if random.choice([True, False]):
        at = random.choice(türkmen_erkek_atlary)
    else:
        at = random.choice(türkmen_aýal_atlary)
    
    familýa = random.choice(türkmen_familýalary)
    
    # Dürli formatlar
    format_görnüşi = random.randint(1, 5)
    
    if format_görnüşi == 1:
        # at.familýa
        email_ady = at.lower() + "." + familýa.lower()
    elif format_görnüşi == 2:
        # atfamilýa + san
        email_ady = at.lower() + familýa.lower() + str(random.randint(1, 999))
    elif format_görnüşi == 3:
        # at_familýa
        email_ady = at.lower() + "_" + familýa.lower()
    elif format_görnüşi == 4:
        # at + sanlar
        email_ady = at.lower() + str(random.randint(1000, 9999))
    else:
        # familýa + at
        email_ady = familýa.lower() + at.lower()
    
    return email_ady + random.choice(türkmen_email_üpjünler)

# 🔥 GÜÝÇLI USER AGENT DÖRETMEK 🔥
def güýçli_user_agent_döret():
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Android 14; Mobile; rv:109.0) Gecko/121.0 Firefox/121.0",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ]
    return random.choice(user_agents)

# 🔥 GÜÝÇLI HEADERS DÖRETMEK 🔥
def güýçli_headers_döret():
    return {
        "User-Agent": güýçli_user_agent_döret(),
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "tk-TM,tk;q=0.9,ru;q=0.8,en;q=0.7",
        "Accept-Encoding": "gzip, deflate, br",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Cache-Control": "max-age=0",
        "Pragma": "no-cache"
    }

# 🔥 START MESSENGER ÜÇIN GÜÝÇLI POST 🔥
async def start_güýçli_post_iber(maglumatlar):
    # Start messenger endpoint-lary
    start_endpoints = [
        "https://tmstart.me/support",
        "https://tmstart.me/support/",
        "https://start.tm/support",
        "https://start.tm/support/",
        "https://tmstart.me/help",
        "https://tmstart.me/contact"
    ]
    
    üstünlik_sany = 0
    
    for endpoint in start_endpoints:
        try:
            headers = güýçli_headers_döret()
            
            # SSL context döretmek
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            connector = aiohttp.TCPConnector(ssl=ssl_context, limit=100, limit_per_host=30)
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            
            async with aiohttp.ClientSession(
                headers=headers, 
                connector=connector, 
                timeout=timeout
            ) as sessiýa:
                
                # Dürli format bilen ibermek
                for format_görnüşi in range(3):
                    try:
                        if format_görnüşi == 0:
                            # Form data
                            jogap = await sessiýa.post(endpoint, data=maglumatlar)
                        elif format_görnüşi == 1:
                            # JSON format
                            jogap = await sessiýa.post(endpoint, json=maglumatlar)
                        else:
                            # URL encoded
                            encoded_data = urlencode(maglumatlar)
                            jogap = await sessiýa.post(endpoint, data=encoded_data, 
                                                     headers={**headers, "Content-Type": "application/x-www-form-urlencoded"})
                        
                        if jogap.status in [200, 201, 202, 204]:
                            üstünlik_sany += 1
                            pystyle.Write.Print(f'✅ Start-a habar iberildi! ({endpoint}) - Status: {jogap.status}\n', 
                                              pystyle.Colors.green, interval=0.0001)
                        else:
                            pystyle.Write.Print(f'⚠️  Garaşylýar... ({endpoint}) - Status: {jogap.status}\n', 
                                              pystyle.Colors.yellow, interval=0.0001)
                        
                        # Kiçi garaşmak
                        await asyncio.sleep(random.uniform(0.1, 0.3))
                        
                    except Exception as e:
                        pystyle.Write.Print(f'⚠️  Gaýtadan synanyşýar... ({endpoint})\n', 
                                          pystyle.Colors.yellow, interval=0.0001)
                        continue
                        
        except Exception as e:
            pystyle.Write.Print(f'⚠️  Endpoint üýtgedilýär... ({endpoint})\n', 
                              pystyle.Colors.yellow, interval=0.0001)
            continue
    
    return üstünlik_sany

# 🔥 PARALEL START HABAR IBERMEK 🔥
async def paralel_start_habar_iber(ulanyjy_ady, tekst, mukdar):
    tasks = []
    
    for i in range(mukdar):
        email = türkmen_email_döret()
        telefon = türkmen_telefon_döret()
        
        maglumatlar = {
            "username": ulanyjy_ady,
            "message": tekst,
            "email": email,
            "phone": telefon,
            "subject": "Start Messenger - Bozgunçylyk Habary",
            "category": "abuse",
            "priority": "high",
            "type": "report",
            "platform": "start_messenger"
        }
        
        task = start_güýçli_post_iber(maglumatlar)
        tasks.append(task)
        
        # Her 5 task-dan soň kiçi garaşmak
        if (i + 1) % 5 == 0:
            await asyncio.sleep(random.uniform(0.5, 1.0))
    
    # Ähli task-lary paralel işletmek
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    üstünlik_jemi = sum(result for result in results if isinstance(result, int))
    pystyle.Write.Print(f'\n🎯 JEMI NETIJE: {üstünlik_jemi} habar üstünlikli iberildi!\n', 
                      pystyle.Colors.green, interval=0.0001)
    
    return üstünlik_jemi

# 🔥 ESASY START GÜÝÇLI IBERMEK FUNKSIÝASY 🔥
async def start_güýçli_iber(ulanyjy_ady, tekst):
    mukdar = int(pystyle.Write.Input("\n🔢 Habarlaryň sany (has köp = has güýçli): ", pystyle.Colors.cyan, interval=0.0001))
    
    pystyle.Write.Print(f"\n🚀 {mukdar} sany güýçli habar Start-a iberilýär...\n", pystyle.Colors.cyan, interval=0.0001)
    pystyle.Write.Print("⚡ GÜÝÇLI START SNOS BAŞLANDY! ⚡\n", pystyle.Colors.red, interval=0.0001)
    
    # Paralel ibermek
    üstünlik_sany = await paralel_start_habar_iber(ulanyjy_ady, tekst, mukdar)
    
    if üstünlik_sany > 0:
        pystyle.Write.Print(f"\n🔥 START SNOS TAMAMLANDY! {üstünlik_sany} habar iberildi! 🔥\n", 
                          pystyle.Colors.green, interval=0.0001)
        pystyle.Write.Print("💀 START GOLDAW GULLUGY HÖKMAN JOGAP BERER! 💀\n", 
                          pystyle.Colors.red, interval=0.0001)
    else:
        pystyle.Write.Print("\n❌ Habar iberip bolmady. Gaýtadan synanyşyň.\n",
                          pystyle.Colors.red, interval=0.0001)

# 🔥 ESASY START MENÝU 🔥
def start_menýu():
    pystyle.System.Clear()
    pystyle.Write.Print("""
    ╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
    ║                                                                                                              ║
    ║      ███████╗████████╗ █████╗ ██████╗ ████████╗    ███████╗███╗   ██╗ ██████╗ ███████╗                     ║
    ║      ██╔════╝╚══██╔══╝██╔══██╗██╔══██╗╚══██╔══╝    ██╔════╝████╗  ██║██╔═══██╗██╔════╝                     ║
    ║      ███████╗   ██║   ███████║██████╔╝   ██║       ███████╗██╔██╗ ██║██║   ██║███████╗                     ║
    ║      ╚════██║   ██║   ██╔══██║██╔══██╗   ██║       ╚════██║██║╚██╗██║██║   ██║╚════██║                     ║
    ║      ███████║   ██║   ██║  ██║██║  ██║   ██║       ███████║██║ ╚████║╚██████╔╝███████║                     ║
    ║      ╚══════╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝       ╚══════╝╚═╝  ╚═══╝ ╚═════╝ ╚══════╝                     ║
    ║                                                                                                              ║
    ║                          🇹🇲 START MESSENGER ÜÇIN GÜÝÇLI SNOS 🇹🇲                                          ║
    ║                        ⚡ Ýasaýjy: Benjamin Franklin | Goldaw Start: @onion ⚡                              ║
    ║                                                                                                              ║
    ║══════════════════════════════════════════════════════════════════════════════════════════════════════════════║
    ║                                                                                                              ║
    ║    🔥 [1] 👤 ULANYJYNY GÜÝÇLI ÝAPMAK           🔥 [4] 💬 TOPARY GÜÝÇLI ÝAPMAK                              ║
    ║    ⚡ [2] 📺 KANALY GÜÝÇLI ÝAPMAK              ⚡ [5] ✍️  ÖZ GÜÝÇLI TEKSTIŇIZ                                ║
    ║    💀 [3] 🤖 BOTY GÜÝÇLI ÝAPMAK               💀 [6] 🚪 ÇYKMAK                                               ║
    ║                                                                                                              ║
    ║                              ⚠️  DUÝDURYŞ: BU PROGRAMMA START-Y HÖKMAN SNOS EDÝÄR! ⚠️                       ║
    ║                                                                                                              ║
    ╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
""", pystyle.Colors.cyan, interval=0.0001)

if __name__ == '__main__':
    while True:
        try:
            start_menýu()
            esasy_saýlaw = int(pystyle.Write.Input("🎯 Opsiýany saýlaň: ", pystyle.Colors.cyan, interval=0.0001))

            if esasy_saýlaw == 1:
                pystyle.Write.Print("👤 Ulanyjyny ýapmak üçin:\n", pystyle.Colors.cyan, interval=0.0001)
                ulanyjy_ady = pystyle.Write.Input("👤 Start ulanyjy ady: ", pystyle.Colors.cyan, interval=0.0001)

                pystyle.Write.Print("🎯 Indi şu sebäpleriň birini saýlaň:\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("1. 📧 Spam habarlary\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("2. 🔍 Şahsy maglumatlary ýaýratmak\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("3. 😈 Kemsitmek we trolling\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("4. 🚫 Bikanun mazmun\n", pystyle.Colors.cyan, interval=0.0001)
                pystyle.Write.Print("5. 💊 Neşe söwdasy\n", pystyle.Colors.cyan, interval=0.0001)

                opsiýa = int(pystyle.Write.Input("➤ ", pystyle.Colors.cyan, interval=0.0001))

                # 🔥 GÜÝÇLI START HABAR TEKSTLERI 🔥
                start_habar_tekstleri = {
                    1: f"Hormatly Start Messenger goldaw gullugy!\n\nMen @{ulanyjy_ady} atly ulanyjydan köpçülikleýin spam habarlary alýaryn. Bu ulanyjy:\n\n• Günde ýüzlerçe spam habar iberýär\n• Reklama we zyýanly baglanyşyklar paýlaşýar\n• Beýleki ulanyjylary biynjalyk edýär\n• Start platformasynyň düzgünlerini bozýar\n\nBu ulanyjynyň hasabyny petiklemegi haýyş edýärin.\n\nHormatlar bilen,\nStart ulanyjysy",

                    2: f"Hormatly Start goldaw gullugy!\n\nMen @{ulanyjy_ady} ulanyjysynyň şahsy maglumatlary ýaýratýandygyny habar bermek isleýärin:\n\n• Adamlaryň telefon nomerlerini paýlaşýar\n• Şahsy suratlary razylyk bolmazdan ýaýradýar\n• Öý salgylaryny we şahsy maglumatlary açýar\n• Bu hareket bikanundyr we howply\n\nDerrew çäre görmegi haýyş edýärin.\n\nHormatlar bilen,\nAlada edýän ulanyjy",

                    3: f"Hormatly Start moderatorlary!\n\n@{ulanyjy_ady} ulanyjysy beýleki adamlary kemsidýär we trolling edýär:\n\n• Edepsiz sözleri ulanýar\n• Adamlary aşak görýär we kemsidýär\n• Jynsy we aýratyn toparlara garşy ýigrençli sözler aýdýar\n• Start-yň dostlukly gurşawyny bozýar\n\nBu ulanyjynyň hasabyny ýapmagyňyzy haýyş edýärin.\n\nHormatlar bilen,\nStart jemgyýeti agzasy",

                    4: f"Hormatly Start howpsuzlyk topary!\n\n@{ulanyjy_ady} ulanyjysy bikanun mazmun paýlaşýar:\n\n• 18+ mazmun we şok materiallar\n• Zorluk we rehimsizlik görkezýän wideolar\n• Çagalar üçin zyýanly mazmun\n• Türkmenistanyň kanunlaryna garşy materiallar\n\nDerrew hasaby petikläň we mazmuny aýyryň.\n\nHormatlar bilen,\nAlada edýän ene-ata",

                    5: f"Hormatly Start goldaw gullugy!\n\n@{ulanyjy_ady} ulanyjysy bikanun neşe söwdasy edýär:\n\n• Neşe serişdelerini satmaga synanyşýar\n• Ýaş adamlara zyýanly maddalar hödürleýär\n• Bu hareket Türkmenistanyň kanunlaryna garşy\n• Jemgyýete uly zyýan ýetirýär\n\nDerrew hasaby petikläň we kanun goraýjy edaralara habar beriň.\n\nHormatlar bilen,\nJemgyýet üçin alada edýän raýat"
                }

                if opsiýa in start_habar_tekstleri:
                    await start_güýçli_iber(ulanyjy_ady, start_habar_tekstleri[opsiýa])

            elif esasy_saýlaw == 6:
                pystyle.Write.Print("🚪 Hoş gal! Start Snos ýapylýar...", pystyle.Colors.cyan, interval=0.0001)
                sys.exit()
            else:
                pystyle.Write.Print("⚠️  Bu opsiýa heniz taýýar däl. Ulanyjy ýapmak (1) ýa-da çykmak (6) saýlaň.\n", pystyle.Colors.yellow, interval=0.0001)
                input("⏎ Dowam etmek üçin Enter basyň...")

        except KeyboardInterrupt:
            pystyle.Write.Print("🛑 Programma ulanyjy tarapyndan ýapyldy!", pystyle.Colors.red, interval=0.0001)
            sys.exit()
        except ValueError:
            pystyle.Write.Print("❌ Nädogry giriş! Diňe san giriziň.", pystyle.Colors.red, interval=0.0001)
            input("⏎ Dowam etmek üçin Enter basyň...")
        except Exception as e:
            pystyle.Write.Print(f"❌ Ýalňyşlyk ýüze çykdy! {str(e)}\n", pystyle.Colors.red, interval=0.0001)
            input("⏎ Dowam etmek üçin Enter basyň...")
